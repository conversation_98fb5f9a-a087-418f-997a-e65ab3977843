// Configuration utility
export const getApiBaseUrl = (): string => {
  // Try runtime config first (from window.APP_CONFIG)
  if (typeof window !== 'undefined' && window.APP_CONFIG?.API_BASE_URL) {
    return window.APP_CONFIG.API_BASE_URL;
  }
  
  // Fallback to Vite environment variable (build-time)
  return import.meta.env.VITE_API_BASE_URL || 'http://localhost:8082';
};

// Export the API base URL
export const API_BASE_URL = getApiBaseUrl();
