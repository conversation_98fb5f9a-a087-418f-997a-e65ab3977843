apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-deployment
  labels:
    app: frontend-app
  namespace: line
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend-app
  template:
    metadata:
      labels:
        app: frontend-app
    spec:
      containers:
      - name: frontend-app
        image: rubyhcm/frontend-line:latest
        ports:
        - containerPort: 9000
        env:
        - name: VITE_API_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: frontend-config
              key: VITE_API_BASE_URL
        # Alternative: Load all ConfigMap values as environment variables
        # envFrom:
        # - configMapRef:
        #     name: frontend-config
