apiVersion: v1
kind: Service
metadata:
  name: frontend-service
  namespace: line
spec:
  type: NodePort
  selector:
    app: frontend-app
  ports:
      # By default and for convenience, the `targetPort` is set to the same value as the `port` field.
    - port: 80
      targetPort: 9000
      # Optional field
      # By default and for convenience, the Kubernetes control plane will allocate a port from a range (default: 30000-32767)
      nodePort: 40000
